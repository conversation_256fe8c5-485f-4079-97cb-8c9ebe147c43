/*
 * Copyright (c) 2023 Lark Technologies Pte. Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.lark.project.service.workitem_conf;

import com.lark.project.core.Config;
import com.lark.project.core.Transport;
import com.lark.project.core.exception.ErrConstants;
import com.lark.project.core.request.RequestOptions;
import com.lark.project.core.response.RawResponse;
import com.lark.project.core.utils.Logs;
import com.lark.project.core.utils.UnmarshalRespUtil;
import com.lark.project.service.workitem_conf.builder.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class WorkItemConfServiceImpl implements WorkItemConfService {

    private static final Logger log = LoggerFactory.getLogger(WorkItemConfServiceImpl.class);

    private Config config;

    public WorkItemConfServiceImpl(Config config) {
        this.config = config;
    }

    // 新增流程类型配置
    public CreateTemplateDetailResp createTemplateDetail(CreateTemplateDetailReq req, RequestOptions reqOptions) throws Exception {
        if (reqOptions == null) {
            reqOptions = new RequestOptions();
        }

        RawResponse httpResponse = Transport.doSend(config, reqOptions, "POST"
                , "/open_api/template/v2/create_template"
                , false
                , req);

        CreateTemplateDetailResp resp = UnmarshalRespUtil.unmarshalResp(httpResponse, CreateTemplateDetailResp.class);
        if (resp == null) {
            log.error(Logs.formatReq(req, httpResponse, "/open_api/template/v2/create_template"));
            throw new IllegalArgumentException(ErrConstants.RESULT_ILLEGAL);
        }

        resp.setRawResponse(httpResponse);
        resp.setRequest(req);

        return resp;
    }

    // 删除流程类型配置
    public DeleteTemplateDetailResp deleteTemplateDetail(DeleteTemplateDetailReq req, RequestOptions reqOptions) throws Exception {
        if (reqOptions == null) {
            reqOptions = new RequestOptions();
        }

        RawResponse httpResponse = Transport.doSend(config, reqOptions, "DELETE"
                , "/open_api/template/v2/delete_template/:project_key/:template_id"
                , false
                , req);

        DeleteTemplateDetailResp resp = UnmarshalRespUtil.unmarshalResp(httpResponse, DeleteTemplateDetailResp.class);
        if (resp == null) {
            log.error(Logs.formatReq(req, httpResponse, "/open_api/template/v2/delete_template/:project_key/:template_id"));
            throw new IllegalArgumentException(ErrConstants.RESULT_ILLEGAL);
        }

        resp.setRawResponse(httpResponse);
        resp.setRequest(req);

        return resp;
    }

    // 获取流程类型配置详情
    public QueryTemplateDetailResp queryTemplateDetail(QueryTemplateDetailReq req, RequestOptions reqOptions) throws Exception {
        if (reqOptions == null) {
            reqOptions = new RequestOptions();
        }

        RawResponse httpResponse = Transport.doSend(config, reqOptions, "GET"
                , "/open_api/:project_key/template_detail/:template_id"
                , false
                , req);

        QueryTemplateDetailResp resp = UnmarshalRespUtil.unmarshalResp(httpResponse, QueryTemplateDetailResp.class);
        if (resp == null) {
            log.error(Logs.formatReq(req, httpResponse, "/open_api/:project_key/template_detail/:template_id"));
            throw new IllegalArgumentException(ErrConstants.RESULT_ILLEGAL);
        }

        resp.setRawResponse(httpResponse);
        resp.setRequest(req);

        return resp;
    }

    // 获取流程类型配置（wbs）
    public QueryWbsTemplateConfResp queryWbsTemplateConf(QueryWbsTemplateConfReq req, RequestOptions reqOptions) throws Exception {
        if (reqOptions == null) {
            reqOptions = new RequestOptions();
        }

        RawResponse httpResponse = Transport.doSend(config, reqOptions, "POST"
                , "/open_api/:project_key/wbs_template"
                , false
                , req);

        QueryWbsTemplateConfResp resp = UnmarshalRespUtil.unmarshalResp(httpResponse, QueryWbsTemplateConfResp.class);
        if (resp == null) {
            log.error(Logs.formatReq(req, httpResponse, "/open_api/:project_key/wbs_template"));
            throw new IllegalArgumentException(ErrConstants.RESULT_ILLEGAL);
        }

        resp.setRawResponse(httpResponse);
        resp.setRequest(req);

        return resp;
    }

    // 获取工作项下的流程类型
    public QueryWorkItemTemplatesResp queryWorkItemTemplates(QueryWorkItemTemplatesReq req, RequestOptions reqOptions) throws Exception {
        if (reqOptions == null) {
            reqOptions = new RequestOptions();
        }

        RawResponse httpResponse = Transport.doSend(config, reqOptions, "GET"
                , "/open_api/:project_key/template_list/:work_item_type_key"
                , false
                , req);

        QueryWorkItemTemplatesResp resp = UnmarshalRespUtil.unmarshalResp(httpResponse, QueryWorkItemTemplatesResp.class);
        if (resp == null) {
            log.error(Logs.formatReq(req, httpResponse, "/open_api/:project_key/template_list/:work_item_type_key"));
            throw new IllegalArgumentException(ErrConstants.RESULT_ILLEGAL);
        }

        resp.setRawResponse(httpResponse);
        resp.setRequest(req);

        return resp;
    }

    // 更新流程类型配置
    public UpdateTemplateDetailResp updateTemplateDetail(UpdateTemplateDetailReq req, RequestOptions reqOptions) throws Exception {
        if (reqOptions == null) {
            reqOptions = new RequestOptions();
        }

        RawResponse httpResponse = Transport.doSend(config, reqOptions, "PUT"
                , "/open_api/template/v2/update_template"
                , false
                , req);

        UpdateTemplateDetailResp resp = UnmarshalRespUtil.unmarshalResp(httpResponse, UpdateTemplateDetailResp.class);
        if (resp == null) {
            log.error(Logs.formatReq(req, httpResponse, "/open_api/template/v2/update_template"));
            throw new IllegalArgumentException(ErrConstants.RESULT_ILLEGAL);
        }

        resp.setRawResponse(httpResponse);
        resp.setRequest(req);

        return resp;
    }

}