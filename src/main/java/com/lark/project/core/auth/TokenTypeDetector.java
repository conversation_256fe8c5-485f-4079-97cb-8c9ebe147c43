/*
 * Copyright (c) 2023 Lark Technologies Pte. Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.lark.project.core.auth;

/**
 * Token类型检测工具类
 */
public class TokenTypeDetector {

    /**
     * Token类型枚举
     */
    public enum TokenType {
        /**
         * 插件访问凭证
         */
        PLUGIN_ACCESS_TOKEN,

        /**
         * 虚拟插件访问凭证
         */
        VIRTUAL_PLUGIN_TOKEN,

        /**
         * 用户访问凭证
         */
        USER_ACCESS_TOKEN,

        /**
         * 未知类型
         */
        UNKNOWN
    }

    /**
     * 根据token前缀检测token类型
     * 
     * @param token 待检测的token
     * @return token类型
     */
    public static TokenType detectTokenType(String token) {
        if (token == null || token.trim().isEmpty()) {
            return TokenType.UNKNOWN;
        }

        String trimmedToken = token.trim();
        
        // plugin_access_token格式: p-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
        if (trimmedToken.startsWith("p-")) {
            return TokenType.PLUGIN_ACCESS_TOKEN;
        }
        
        // virtual_plugin_token格式: v-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
        if (trimmedToken.startsWith("v-")) {
            return TokenType.VIRTUAL_PLUGIN_TOKEN;
        }
        
        // user_access_token格式: u-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
        if (trimmedToken.startsWith("u-")) {
            return TokenType.USER_ACCESS_TOKEN;
        }

        return TokenType.UNKNOWN;
    }

    /**
     * 检查是否为插件访问凭证
     * 
     * @param token 待检测的token
     * @return 是否为插件访问凭证
     */
    public static boolean isPluginAccessToken(String token) {
        return detectTokenType(token) == TokenType.PLUGIN_ACCESS_TOKEN;
    }

    /**
     * 检查是否为虚拟插件访问凭证
     * 
     * @param token 待检测的token
     * @return 是否为虚拟插件访问凭证
     */
    public static boolean isVirtualPluginToken(String token) {
        return detectTokenType(token) == TokenType.VIRTUAL_PLUGIN_TOKEN;
    }

    /**
     * 检查是否为用户访问凭证
     * 
     * @param token 待检测的token
     * @return 是否为用户访问凭证
     */
    public static boolean isUserAccessToken(String token) {
        return detectTokenType(token) == TokenType.USER_ACCESS_TOKEN;
    }
}
