/*
 * Copyright (c) 2023 Lark Technologies Pte. Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.lark.project.core.auth;

/**
 * 身份验证校验结果
 */
public class AuthValidationResult {

    private final boolean success;
    private final String message;
    private final String token;
    private final int expireTime;
    private final ErrorType errorType;
    private final int errorCode;

    private AuthValidationResult(boolean success, String message, String token, int expireTime, ErrorType errorType, int errorCode) {
        this.success = success;
        this.message = message;
        this.token = token;
        this.expireTime = expireTime;
        this.errorType = errorType;
        this.errorCode = errorCode;
    }

    /**
     * 创建成功的校验结果
     * 
     * @param message 成功消息
     * @param token 有效的token
     * @param expireTime token过期时间（秒级时间戳）
     * @return 校验结果
     */
    public static AuthValidationResult success(String message, String token, int expireTime) {
        return new AuthValidationResult(true, message, token, expireTime, null, 0);
    }

    /**
     * 创建失败的校验结果
     * 
     * @param errorType 错误类型
     * @param message 错误消息
     * @param errorCode 错误代码
     * @return 校验结果
     */
    public static AuthValidationResult failure(ErrorType errorType, String message, int errorCode) {
        return new AuthValidationResult(false, message, null, -1, errorType, errorCode);
    }

    /**
     * 是否校验成功
     * 
     * @return true表示校验成功，false表示校验失败
     */
    public boolean isSuccess() {
        return success;
    }

    /**
     * 获取校验消息
     * 
     * @return 校验消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 获取有效的token（仅在校验成功时有值）
     * 
     * @return token字符串
     */
    public String getToken() {
        return token;
    }

    /**
     * 获取token过期时间（仅在校验成功时有值）
     * 
     * @return 过期时间（秒级时间戳），-1表示未知
     */
    public int getExpireTime() {
        return expireTime;
    }

    /**
     * 获取错误类型（仅在校验失败时有值）
     * 
     * @return 错误类型
     */
    public ErrorType getErrorType() {
        return errorType;
    }

    /**
     * 获取错误代码（仅在校验失败时有值）
     * 
     * @return 错误代码
     */
    public int getErrorCode() {
        return errorCode;
    }

    /**
     * 检查token是否已过期
     * 
     * @return true表示已过期，false表示未过期或无法判断
     */
    public boolean isExpired() {
        if (!success || expireTime <= 0) {
            return false;
        }
        long currentTime = System.currentTimeMillis() / 1000; // 转换为秒级时间戳
        return currentTime >= expireTime;
    }

    /**
     * 获取token剩余有效时间（秒）
     * 
     * @return 剩余有效时间，-1表示无法计算
     */
    public long getRemainingTime() {
        if (!success || expireTime <= 0) {
            return -1;
        }
        long currentTime = System.currentTimeMillis() / 1000;
        long remaining = expireTime - currentTime;
        return Math.max(0, remaining);
    }

    @Override
    public String toString() {
        if (success) {
            return String.format("AuthValidationResult{success=true, message='%s', token='%s', expireTime=%d, remainingTime=%d}",
                    message, token != null ? token.substring(0, Math.min(10, token.length())) + "..." : "null", 
                    expireTime, getRemainingTime());
        } else {
            return String.format("AuthValidationResult{success=false, message='%s', errorType=%s, errorCode=%d}",
                    message, errorType, errorCode);
        }
    }

    /**
     * 错误类型枚举
     */
    public enum ErrorType {
        /**
         * 无效的凭证（Plugin ID或Secret错误）
         */
        INVALID_CREDENTIALS,

        /**
         * 无效的Token
         */
        INVALID_TOKEN,

        /**
         * Token已过期
         */
        TOKEN_EXPIRED,

        /**
         * 网络连接错误
         */
        NETWORK_ERROR,

        /**
         * 服务器错误
         */
        SERVER_ERROR,

        /**
         * 权限不足
         */
        PERMISSION_DENIED,

        /**
         * 其他未知错误
         */
        UNKNOWN_ERROR
    }
}
