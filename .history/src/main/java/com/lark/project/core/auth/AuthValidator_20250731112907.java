/*
 * Copyright (c) 2023 Lark Technologies Pte. Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.lark.project.core.auth;

import com.lark.project.Client;
import com.lark.project.core.request.RequestOptions;
import com.lark.project.service.plugin.builder.GetPluginTokenReq;
import com.lark.project.service.plugin.builder.GetPluginTokenResp;
import com.lark.project.service.user.builder.QueryUserDetailReq;
import com.lark.project.service.user.builder.QueryUserDetailResp;

/**
 * 身份验证校验工具类
 * 支持三种访问凭证类型的校验：
 * 1. plugin_access_token - 插件访问凭证
 * 2. virtual_plugin_token - 虚拟插件访问凭证  
 * 3. user_access_token - 用户访问凭证
 */
public class AuthValidator {

    /**
     * 校验Plugin ID和Secret是否有效
     * 通过尝试获取plugin_access_token来验证
     *
     * @param pluginId 插件ID
     * @param pluginSecret 插件密钥
     * @return 校验结果
     */
    public static AuthValidationResult validatePluginCredentials(String pluginId, String pluginSecret) {
        return validatePluginCredentials(pluginId, pluginSecret, null, null);
    }

    /**
     * 校验Plugin ID和Secret是否有效
     *
     * @param pluginId 插件ID
     * @param pluginSecret 插件密钥
     * @param baseUrl 自定义API基础URL，为null时使用默认URL
     * @return 校验结果
     */
    public static AuthValidationResult validatePluginCredentials(String pluginId, String pluginSecret, String baseUrl) {
        return validatePluginCredentials(pluginId, pluginSecret, null, baseUrl);
    }

    /**
     * 校验Plugin ID、Secret和User Key组合是否有效
     * 先获取plugin_access_token，然后尝试调用API验证token+userKey是否可用
     *
     * @param pluginId 插件ID
     * @param pluginSecret 插件密钥
     * @param userKey 用户标识（可选，如果提供则会验证token+userKey的组合）
     * @param baseUrl 自定义API基础URL，为null时使用默认URL
     * @return 校验结果
     */
    public static AuthValidationResult validatePluginCredentials(String pluginId, String pluginSecret, String userKey, String baseUrl) {
        try {
            Client.Builder builder = Client.newBuilder(pluginId, pluginSecret);
            if (baseUrl != null && !baseUrl.isEmpty()) {
                builder.openBaseUrl(baseUrl);
            }
            Client client = builder.build();

            // 尝试获取plugin token来验证凭证
            GetPluginTokenReq req = GetPluginTokenReq.newBuilder()
                    .pluginID(pluginId)
                    .pluginSecret(pluginSecret)
                    .type(0) // AccessTokenTypePlugin
                    .build();

            GetPluginTokenResp resp = client.getPluginService().getPluginToken(req, null);

            if (resp.getErr() != null) {
                return AuthValidationResult.failure(
                    AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
                    "Plugin凭证无效: " + resp.getErr().getMsg(),
                    resp.getErr().getCode()
                );
            }

            if (resp.getData() == null || resp.getData().getToken() == null) {
                return AuthValidationResult.failure(
                    AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
                    "Plugin凭证无效: 未能获取到有效token",
                    -1
                );
            }

            // 如果提供了userKey，则进一步验证token+userKey的组合是否可用
            if (userKey != null && !userKey.isEmpty()) {
                try {
                    QueryUserDetailReq userReq = QueryUserDetailReq.newBuilder().build();
                    QueryUserDetailResp userResp = client.getUserService().queryUserDetail(
                            userReq,
                            RequestOptions.newBuilder().userKey(userKey).build()
                    );

                    if (userResp.getErr() != null) {
                        return AuthValidationResult.failure(
                            AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
                            "Plugin凭证有效但User Key无效: " + userResp.getErr().getMsg(),
                            userResp.getErr().getCode()
                        );
                    }

                    return AuthValidationResult.success(
                        "Plugin凭证和User Key验证成功",
                        resp.getData().getToken(),
                        resp.getData().getExpireTime()
                    );
                } catch (Exception userEx) {
                    return AuthValidationResult.failure(
                        AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
                        "Plugin凭证有效但User Key验证失败: " + userEx.getMessage(),
                        -1
                    );
                }
            }

            return AuthValidationResult.success(
                "Plugin凭证验证成功",
                resp.getData().getToken(),
                resp.getData().getExpireTime()
            );

        } catch (Exception e) {
            return AuthValidationResult.failure(
                AuthValidationResult.ErrorType.NETWORK_ERROR,
                "网络连接失败: " + e.getMessage(),
                -1
            );
        }
    }

    /**
     * 校验虚拟插件凭证是否有效
     *
     * @param pluginId 插件ID
     * @param pluginSecret 插件密钥
     * @return 校验结果
     */
    public static AuthValidationResult validateVirtualPluginCredentials(String pluginId, String pluginSecret) {
        return validateVirtualPluginCredentials(pluginId, pluginSecret, null, null);
    }

    /**
     * 校验虚拟插件凭证是否有效
     *
     * @param pluginId 插件ID
     * @param pluginSecret 插件密钥
     * @param baseUrl 自定义API基础URL，为null时使用默认URL
     * @return 校验结果
     */
    public static AuthValidationResult validateVirtualPluginCredentials(String pluginId, String pluginSecret, String baseUrl) {
        return validateVirtualPluginCredentials(pluginId, pluginSecret, null, baseUrl);
    }

    /**
     * 校验虚拟插件凭证和User Key组合是否有效
     * 先获取virtual_plugin_token，然后尝试调用API验证token+userKey是否可用
     *
     * @param pluginId 插件ID
     * @param pluginSecret 插件密钥
     * @param userKey 用户标识（可选，如果提供则会验证token+userKey的组合）
     * @param baseUrl 自定义API基础URL，为null时使用默认URL
     * @return 校验结果
     */
    public static AuthValidationResult validateVirtualPluginCredentials(String pluginId, String pluginSecret, String userKey, String baseUrl) {
        try {
            Client.Builder builder = Client.newBuilder(pluginId, pluginSecret);
            if (baseUrl != null && !baseUrl.isEmpty()) {
                builder.openBaseUrl(baseUrl);
            }
            Client client = builder.build();

            // 尝试获取virtual plugin token来验证凭证
            GetPluginTokenReq req = GetPluginTokenReq.newBuilder()
                    .pluginID(pluginId)
                    .pluginSecret(pluginSecret)
                    .type(1) // AccessTokenTypeVirtualPlugin
                    .build();

            GetPluginTokenResp resp = client.getPluginService().getPluginToken(req, null);

            if (resp.getErr() != null) {
                return AuthValidationResult.failure(
                    AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
                    "虚拟插件凭证无效: " + resp.getErr().getMsg(),
                    resp.getErr().getCode()
                );
            }

            if (resp.getData() == null || resp.getData().getToken() == null) {
                return AuthValidationResult.failure(
                    AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
                    "虚拟插件凭证无效: 未能获取到有效token",
                    -1
                );
            }

            // 如果提供了userKey，则进一步验证token+userKey的组合是否可用
            if (userKey != null && !userKey.isEmpty()) {
                try {
                    QueryUserDetailReq userReq = QueryUserDetailReq.newBuilder().build();
                    QueryUserDetailResp userResp = client.getUserService().queryUserDetail(
                            userReq,
                            RequestOptions.newBuilder().userKey(userKey).build()
                    );

                    if (userResp.getErr() != null) {
                        return AuthValidationResult.failure(
                            AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
                            "虚拟插件凭证有效但User Key无效: " + userResp.getErr().getMsg(),
                            userResp.getErr().getCode()
                        );
                    }

                    return AuthValidationResult.success(
                        "虚拟插件凭证和User Key验证成功",
                        resp.getData().getToken(),
                        resp.getData().getExpireTime()
                    );
                } catch (Exception userEx) {
                    return AuthValidationResult.failure(
                        AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
                        "虚拟插件凭证有效但User Key验证失败: " + userEx.getMessage(),
                        -1
                    );
                }
            }

            return AuthValidationResult.success(
                "虚拟插件凭证验证成功",
                resp.getData().getToken(),
                resp.getData().getExpireTime()
            );

        } catch (Exception e) {
            return AuthValidationResult.failure(
                AuthValidationResult.ErrorType.NETWORK_ERROR,
                "网络连接失败: " + e.getMessage(),
                -1
            );
        }
    }

    /**
     * 校验用户访问凭证格式是否有效（仅格式检查，不调用API）
     *
     * @param userAccessToken 用户访问凭证
     * @return 校验结果
     */
    public static AuthValidationResult validateUserAccessTokenFormat(String userAccessToken) {
        if (userAccessToken == null || userAccessToken.trim().isEmpty()) {
            return AuthValidationResult.failure(
                AuthValidationResult.ErrorType.INVALID_TOKEN,
                "用户访问凭证不能为空",
                -1
            );
        }

        if (!isValidTokenFormat(userAccessToken) || !TokenTypeDetector.isUserAccessToken(userAccessToken)) {
            return AuthValidationResult.failure(
                AuthValidationResult.ErrorType.INVALID_TOKEN,
                "用户访问凭证格式无效，应以'u-'开头",
                -1
            );
        }

        return AuthValidationResult.success(
            "用户访问凭证格式验证成功",
            userAccessToken,
            -1
        );
    }

    /**
     * 校验用户访问凭证是否有效
     * 注意：由于SDK架构限制，仍需要提供Plugin ID和Secret来构建Client，
     * 但实际验证时使用的是User Access Token
     *
     * @param userAccessToken 用户访问凭证
     * @param pluginId 插件ID（用于构建客户端，但不用于验证）
     * @param pluginSecret 插件密钥（用于构建客户端，但不用于验证）
     * @return 校验结果
     */
    public static AuthValidationResult validateUserAccessToken(String userAccessToken, String pluginId, String pluginSecret) {
        return validateUserAccessToken(userAccessToken, pluginId, pluginSecret, null);
    }

    /**
     * 校验用户访问凭证是否有效
     * 注意：由于SDK架构限制，仍需要提供Plugin ID和Secret来构建Client，
     * 但实际验证时使用的是User Access Token
     *
     * @param userAccessToken 用户访问凭证
     * @param pluginId 插件ID（用于构建客户端，但不用于验证，可以传空字符串）
     * @param pluginSecret 插件密钥（用于构建客户端，但不用于验证，可以传空字符串）
     * @param baseUrl 自定义API基础URL，为null时使用默认URL
     * @return 校验结果
     */
    public static AuthValidationResult validateUserAccessToken(String userAccessToken, String pluginId, String pluginSecret, String baseUrl) {
        try {
            // 先检查token格式
            if (!isValidTokenFormat(userAccessToken) || !TokenTypeDetector.isUserAccessToken(userAccessToken)) {
                return AuthValidationResult.failure(
                    AuthValidationResult.ErrorType.INVALID_TOKEN,
                    "用户访问凭证格式无效，应以'u-'开头",
                    -1
                );
            }

            // 由于SDK架构限制，需要Plugin ID和Secret来构建Client
            // 但实际API调用时使用的是User Access Token
            // 对于User Access Token验证，可以传入空字符串作为占位符
            String dummyPluginId = (pluginId != null && !pluginId.isEmpty()) ? pluginId : "dummy";
            String dummyPluginSecret = (pluginSecret != null && !pluginSecret.isEmpty()) ? pluginSecret : "dummy";

            Client.Builder builder = Client.newBuilder(dummyPluginId, dummyPluginSecret)
                    .disableTokenCache(); // 禁用token缓存，因为我们要使用自己的token

            if (baseUrl != null && !baseUrl.isEmpty()) {
                builder.openBaseUrl(baseUrl);
            }
            Client client = builder.build();

            // 构建请求选项，传入用户访问凭证
            RequestOptions options = RequestOptions.newBuilder()
                    .accessToken(userAccessToken)
                    .build();

            // 尝试调用用户详情接口来验证token有效性
            QueryUserDetailReq req = QueryUserDetailReq.newBuilder().build();
            QueryUserDetailResp resp = client.getUserService().queryUserDetail(req, options);

            if (resp.getErr() != null) {
                return AuthValidationResult.failure(
                    AuthValidationResult.ErrorType.INVALID_TOKEN,
                    "用户访问凭证无效: " + resp.getErr().getMsg(),
                    resp.getErr().getCode()
                );
            }

            return AuthValidationResult.success(
                "用户访问凭证验证成功",
                userAccessToken,
                -1 // 用户token过期时间需要从token本身解析，这里暂时返回-1
            );

        } catch (Exception e) {
            return AuthValidationResult.failure(
                AuthValidationResult.ErrorType.NETWORK_ERROR,
                "网络连接失败: " + e.getMessage(),
                -1
            );
        }
    }

    /**
     * 简单的token格式校验
     * 
     * @param token 待校验的token
     * @return 是否符合基本格式要求
     */
    public static boolean isValidTokenFormat(String token) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }

        // 基本格式校验
        String trimmedToken = token.trim();
        
        // plugin_access_token格式: p-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
        if (trimmedToken.startsWith("p-") && trimmedToken.length() > 10) {
            return true;
        }
        
        // virtual_plugin_token格式: v-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
        if (trimmedToken.startsWith("v-") && trimmedToken.length() > 10) {
            return true;
        }
        
        // user_access_token格式: u-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
        if (trimmedToken.startsWith("u-") && trimmedToken.length() > 10) {
            return true;
        }

        return false;
    }
}
