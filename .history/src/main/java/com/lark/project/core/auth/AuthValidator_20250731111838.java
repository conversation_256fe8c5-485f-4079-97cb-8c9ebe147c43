/*
 * Copyright (c) 2023 Lark Technologies Pte. Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.lark.project.core.auth;

import com.lark.project.Client;
import com.lark.project.core.request.RequestOptions;
import com.lark.project.service.plugin.builder.GetPluginTokenReq;
import com.lark.project.service.plugin.builder.GetPluginTokenResp;
import com.lark.project.service.user.builder.QueryUserDetailReq;
import com.lark.project.service.user.builder.QueryUserDetailResp;

/**
 * 身份验证校验工具类
 * 支持三种访问凭证类型的校验：
 * 1. plugin_access_token - 插件访问凭证
 * 2. virtual_plugin_token - 虚拟插件访问凭证  
 * 3. user_access_token - 用户访问凭证
 */
public class AuthValidator {

    /**
     * 校验Plugin ID和Secret是否有效
     * 通过尝试获取plugin_access_token来验证
     *
     * @param pluginId 插件ID
     * @param pluginSecret 插件密钥
     * @return 校验结果
     */
    public static AuthValidationResult validatePluginCredentials(String pluginId, String pluginSecret) {
        return validatePluginCredentials(pluginId, pluginSecret, null, null);
    }

    /**
     * 校验Plugin ID和Secret是否有效
     *
     * @param pluginId 插件ID
     * @param pluginSecret 插件密钥
     * @param baseUrl 自定义API基础URL，为null时使用默认URL
     * @return 校验结果
     */
    public static AuthValidationResult validatePluginCredentials(String pluginId, String pluginSecret, String baseUrl) {
        return validatePluginCredentials(pluginId, pluginSecret, null, baseUrl);
    }

    /**
     * 校验Plugin ID、Secret和User Key组合是否有效
     * 先获取plugin_access_token，然后尝试调用API验证token+userKey是否可用
     *
     * @param pluginId 插件ID
     * @param pluginSecret 插件密钥
     * @param userKey 用户标识（可选，如果提供则会验证token+userKey的组合）
     * @param baseUrl 自定义API基础URL，为null时使用默认URL
     * @return 校验结果
     */
    public static AuthValidationResult validatePluginCredentials(String pluginId, String pluginSecret, String userKey, String baseUrl) {
        try {
            Client.Builder builder = Client.newBuilder(pluginId, pluginSecret);
            if (baseUrl != null && !baseUrl.isEmpty()) {
                builder.openBaseUrl(baseUrl);
            }
            Client client = builder.build();

            // 尝试获取plugin token来验证凭证
            GetPluginTokenReq req = GetPluginTokenReq.newBuilder()
                    .pluginID(pluginId)
                    .pluginSecret(pluginSecret)
                    .type(0) // AccessTokenTypePlugin
                    .build();

            GetPluginTokenResp resp = client.getPluginService().getPluginToken(req, null);

            if (resp.getErr() != null) {
                return AuthValidationResult.failure(
                    AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
                    "Plugin凭证无效: " + resp.getErr().getMsg(),
                    resp.getErr().getCode()
                );
            }

            if (resp.getData() == null || resp.getData().getToken() == null) {
                return AuthValidationResult.failure(
                    AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
                    "Plugin凭证无效: 未能获取到有效token",
                    -1
                );
            }

            // 如果提供了userKey，则进一步验证token+userKey的组合是否可用
            if (userKey != null && !userKey.isEmpty()) {
                try {
                    QueryUserDetailReq userReq = QueryUserDetailReq.newBuilder().build();
                    QueryUserDetailResp userResp = client.getUserService().queryUserDetail(
                            userReq,
                            RequestOptions.newBuilder().userKey(userKey).build()
                    );

                    if (userResp.getErr() != null) {
                        return AuthValidationResult.failure(
                            AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
                            "Plugin凭证有效但User Key无效: " + userResp.getErr().getMsg(),
                            userResp.getErr().getCode()
                        );
                    }

                    return AuthValidationResult.success(
                        "Plugin凭证和User Key验证成功",
                        resp.getData().getToken(),
                        resp.getData().getExpireTime()
                    );
                } catch (Exception userEx) {
                    return AuthValidationResult.failure(
                        AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
                        "Plugin凭证有效但User Key验证失败: " + userEx.getMessage(),
                        -1
                    );
                }
            }

            return AuthValidationResult.success(
                "Plugin凭证验证成功",
                resp.getData().getToken(),
                resp.getData().getExpireTime()
            );

        } catch (Exception e) {
            return AuthValidationResult.failure(
                AuthValidationResult.ErrorType.NETWORK_ERROR,
                "网络连接失败: " + e.getMessage(),
                -1
            );
        }
    }

    /**
     * 校验虚拟插件凭证是否有效
     *
     * @param pluginId 插件ID
     * @param pluginSecret 插件密钥
     * @return 校验结果
     */
    public static AuthValidationResult validateVirtualPluginCredentials(String pluginId, String pluginSecret) {
        return validateVirtualPluginCredentials(pluginId, pluginSecret, null, null);
    }

    /**
     * 校验虚拟插件凭证是否有效
     *
     * @param pluginId 插件ID
     * @param pluginSecret 插件密钥
     * @param baseUrl 自定义API基础URL，为null时使用默认URL
     * @return 校验结果
     */
    public static AuthValidationResult validateVirtualPluginCredentials(String pluginId, String pluginSecret, String baseUrl) {
        return validateVirtualPluginCredentials(pluginId, pluginSecret, null, baseUrl);
    }

    /**
     * 校验虚拟插件凭证和User Key组合是否有效
     * 先获取virtual_plugin_token，然后尝试调用API验证token+userKey是否可用
     *
     * @param pluginId 插件ID
     * @param pluginSecret 插件密钥
     * @param userKey 用户标识（可选，如果提供则会验证token+userKey的组合）
     * @param baseUrl 自定义API基础URL，为null时使用默认URL
     * @return 校验结果
     */
    public static AuthValidationResult validateVirtualPluginCredentials(String pluginId, String pluginSecret, String userKey, String baseUrl) {
        try {
            Client.Builder builder = Client.newBuilder(pluginId, pluginSecret);
            if (baseUrl != null && !baseUrl.isEmpty()) {
                builder.openBaseUrl(baseUrl);
            }
            Client client = builder.build();

            // 尝试获取virtual plugin token来验证凭证
            GetPluginTokenReq req = GetPluginTokenReq.newBuilder()
                    .pluginID(pluginId)
                    .pluginSecret(pluginSecret)
                    .type(1) // AccessTokenTypeVirtualPlugin
                    .build();

            GetPluginTokenResp resp = client.getPluginService().getPluginToken(req, null);

            if (resp.getErr() != null) {
                return AuthValidationResult.failure(
                    AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
                    "虚拟插件凭证无效: " + resp.getErr().getMsg(),
                    resp.getErr().getCode()
                );
            }

            if (resp.getData() == null || resp.getData().getToken() == null) {
                return AuthValidationResult.failure(
                    AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
                    "虚拟插件凭证无效: 未能获取到有效token",
                    -1
                );
            }

            // 如果提供了userKey，则进一步验证token+userKey的组合是否可用
            if (userKey != null && !userKey.isEmpty()) {
                try {
                    QueryUserDetailReq userReq = QueryUserDetailReq.newBuilder().build();
                    QueryUserDetailResp userResp = client.getUserService().queryUserDetail(
                            userReq,
                            RequestOptions.newBuilder().userKey(userKey).build()
                    );

                    if (userResp.getErr() != null) {
                        return AuthValidationResult.failure(
                            AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
                            "虚拟插件凭证有效但User Key无效: " + userResp.getErr().getMsg(),
                            userResp.getErr().getCode()
                        );
                    }

                    return AuthValidationResult.success(
                        "虚拟插件凭证和User Key验证成功",
                        resp.getData().getToken(),
                        resp.getData().getExpireTime()
                    );
                } catch (Exception userEx) {
                    return AuthValidationResult.failure(
                        AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
                        "虚拟插件凭证有效但User Key验证失败: " + userEx.getMessage(),
                        -1
                    );
                }
            }

            return AuthValidationResult.success(
                "虚拟插件凭证验证成功",
                resp.getData().getToken(),
                resp.getData().getExpireTime()
            );

        } catch (Exception e) {
            return AuthValidationResult.failure(
                AuthValidationResult.ErrorType.NETWORK_ERROR,
                "网络连接失败: " + e.getMessage(),
                -1
            );
        }
    }

    /**
     * 校验用户访问凭证是否有效
     * 通过调用用户详情接口来验证token有效性
     * 
     * @param userAccessToken 用户访问凭证
     * @param pluginId 插件ID（用于构建客户端）
     * @param pluginSecret 插件密钥（用于构建客户端）
     * @return 校验结果
     */
    public static AuthValidationResult validateUserAccessToken(String userAccessToken, String pluginId, String pluginSecret) {
        return validateUserAccessToken(userAccessToken, pluginId, pluginSecret, null, null);
    }

    /**
     * 校验用户访问凭证是否有效
     * 
     * @param userAccessToken 用户访问凭证
     * @param pluginId 插件ID（用于构建客户端）
     * @param pluginSecret 插件密钥（用于构建客户端）
     * @param userKey 用户标识（可选，用于获取用户详情）
     * @param baseUrl 自定义API基础URL，为null时使用默认URL
     * @return 校验结果
     */
    public static AuthValidationResult validateUserAccessToken(String userAccessToken, String pluginId, String pluginSecret, String userKey, String baseUrl) {
        try {
            Client.Builder builder = Client.newBuilder(pluginId, pluginSecret);
            if (baseUrl != null && !baseUrl.isEmpty()) {
                builder.openBaseUrl(baseUrl);
            }
            Client client = builder.build();

            // 构建请求选项，传入用户访问凭证
            RequestOptions.Builder optionsBuilder = RequestOptions.newBuilder()
                    .accessToken(userAccessToken);
            
            if (userKey != null && !userKey.isEmpty()) {
                optionsBuilder.userKey(userKey);
            }

            // 尝试调用用户详情接口来验证token有效性
            QueryUserDetailReq req = QueryUserDetailReq.newBuilder().build();
            QueryUserDetailResp resp = client.getUserService().queryUserDetail(req, optionsBuilder.build());

            if (resp.getErr() != null) {
                return AuthValidationResult.failure(
                    AuthValidationResult.ErrorType.INVALID_TOKEN,
                    "用户访问凭证无效: " + resp.getErr().getMsg(),
                    resp.getErr().getCode()
                );
            }

            return AuthValidationResult.success(
                "用户访问凭证验证成功",
                userAccessToken,
                -1 // 用户token过期时间需要从token本身解析，这里暂时返回-1
            );

        } catch (Exception e) {
            return AuthValidationResult.failure(
                AuthValidationResult.ErrorType.NETWORK_ERROR,
                "网络连接失败: " + e.getMessage(),
                -1
            );
        }
    }

    /**
     * 简单的token格式校验
     * 
     * @param token 待校验的token
     * @return 是否符合基本格式要求
     */
    public static boolean isValidTokenFormat(String token) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }

        // 基本格式校验
        String trimmedToken = token.trim();
        
        // plugin_access_token格式: p-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
        if (trimmedToken.startsWith("p-") && trimmedToken.length() > 10) {
            return true;
        }
        
        // virtual_plugin_token格式: v-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
        if (trimmedToken.startsWith("v-") && trimmedToken.length() > 10) {
            return true;
        }
        
        // user_access_token格式: u-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
        if (trimmedToken.startsWith("u-") && trimmedToken.length() > 10) {
            return true;
        }

        return false;
    }
}
