/*
 * Copyright (c) 2023 Lark Technologies Pte. Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.lark.project.sample;

import com.lark.project.Client;
import com.lark.project.core.auth.AuthValidationResult;
import com.lark.project.core.auth.AuthValidator;
import com.lark.project.core.auth.TokenTypeDetector;

/**
 * 身份验证校验示例
 */
public class AuthValidationSample {

    public static void main(String[] args) {
        // 示例参数
        String pluginId = "your_plugin_id";
        String pluginSecret = "your_plugin_secret";
        String userAccessToken = "u-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx";
        String userKey = "your_user_key";

        // 方式1：使用Client实例进行校验
        testWithClientInstance(pluginId, pluginSecret, userAccessToken, userKey);

        // 方式2：使用AuthValidator静态方法进行校验
        testWithAuthValidator(pluginId, pluginSecret, userAccessToken, userKey);

        // 方式3：Token类型检测
        testTokenTypeDetection();
    }

    /**
     * 使用Client实例进行校验
     */
    private static void testWithClientInstance(String pluginId, String pluginSecret, String userAccessToken, String userKey) {
        System.out.println("=== 使用Client实例进行校验 ===");

        try {
            // 构建客户端
            Client client = Client.newBuilder(pluginId, pluginSecret)
                    .openBaseUrl("https://project.feishu.cn/")
                    .build();

            // 校验Plugin凭证
            AuthValidationResult pluginResult = client.validatePluginCredentials();
            System.out.println("Plugin凭证校验结果: " + pluginResult);

            if (pluginResult.isSuccess()) {
                System.out.println("Plugin凭证有效，token: " + pluginResult.getToken().substring(0, 10) + "...");
                System.out.println("剩余有效时间: " + pluginResult.getRemainingTime() + "秒");
            } else {
                System.out.println("Plugin凭证无效: " + pluginResult.getMessage());
            }

            // 校验用户访问凭证
            if (userAccessToken != null && !userAccessToken.isEmpty()) {
                AuthValidationResult userResult = client.validateUserAccessToken(userAccessToken, userKey);
                System.out.println("用户访问凭证校验结果: " + userResult);

                if (userResult.isSuccess()) {
                    System.out.println("用户访问凭证有效");
                } else {
                    System.out.println("用户访问凭证无效: " + userResult.getMessage());
                }
            }

        } catch (Exception e) {
            System.out.println("校验过程中发生异常: " + e.getMessage());
        }
    }

    /**
     * 使用AuthValidator静态方法进行校验
     */
    private static void testWithAuthValidator(String pluginId, String pluginSecret, String userAccessToken, String userKey) {
        System.out.println("\n=== 使用AuthValidator静态方法进行校验 ===");

        // 校验Plugin凭证
        AuthValidationResult pluginResult = AuthValidator.validatePluginCredentials(pluginId, pluginSecret);
        System.out.println("Plugin凭证校验结果: " + pluginResult);

        // 校验虚拟Plugin凭证
        AuthValidationResult virtualPluginResult = AuthValidator.validateVirtualPluginCredentials(pluginId, pluginSecret);
        System.out.println("虚拟Plugin凭证校验结果: " + virtualPluginResult);

        // 校验用户访问凭证
        if (userAccessToken != null && !userAccessToken.isEmpty()) {
            AuthValidationResult userResult = AuthValidator.validateUserAccessToken(
                    userAccessToken, pluginId, pluginSecret, userKey, null);
            System.out.println("用户访问凭证校验结果: " + userResult);
        }
    }

    /**
     * Token类型检测示例
     */
    private static void testTokenTypeDetection() {
        System.out.println("\n=== Token类型检测示例 ===");

        String[] testTokens = {
                "p-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",  // plugin_access_token
                "v-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",  // virtual_plugin_token
                "u-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",  // user_access_token
                "invalid-token",                            // 无效token
                ""                                          // 空token
        };

        for (String token : testTokens) {
            TokenTypeDetector.TokenType type = TokenTypeDetector.detectTokenType(token);
            System.out.println("Token: " + token + " -> 类型: " + type);

            // 格式校验
            boolean isValid = AuthValidator.isValidTokenFormat(token);
            System.out.println("格式是否有效: " + isValid);
            System.out.println();
        }
    }

    /**
     * 简单的连接测试方法
     * 业务方可以直接使用这个方法进行快速测试
     */
    public static boolean testConnection(String pluginId, String pluginSecret) {
        try {
            AuthValidationResult result = AuthValidator.validatePluginCredentials(pluginId, pluginSecret);
            if (result.isSuccess()) {
                System.out.println("连接测试成功: " + result.getMessage());
                return true;
            } else {
                System.out.println("连接测试失败: " + result.getMessage());
                return false;
            }
        } catch (Exception e) {
            System.out.println("连接测试异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 测试用户token是否有效
     */
    public static boolean testUserToken(String userAccessToken, String pluginId, String pluginSecret) {
        try {
            AuthValidationResult result = AuthValidator.validateUserAccessToken(
                    userAccessToken, pluginId, pluginSecret, null, null);
            if (result.isSuccess()) {
                System.out.println("用户token测试成功: " + result.getMessage());
                return true;
            } else {
                System.out.println("用户token测试失败: " + result.getMessage());
                return false;
            }
        } catch (Exception e) {
            System.out.println("用户token测试异常: " + e.getMessage());
            return false;
        }
    }
}
