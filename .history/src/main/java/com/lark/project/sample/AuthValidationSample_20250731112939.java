/*
 * Copyright (c) 2023 Lark Technologies Pte. Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.lark.project.sample;

import com.lark.project.core.auth.AuthValidationResult;
import com.lark.project.core.auth.AuthValidator;
import com.lark.project.core.auth.TokenTypeDetector;

/**
 * 身份验证校验示例
 */
public class AuthValidationSample {

    public static void main(String[] args) {
        // 示例参数
        String pluginId = "your_plugin_id";
        String pluginSecret = "your_plugin_secret";
        String userAccessToken = "u-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx";
        String userKey = "your_user_key";

        // 方式1：使用AuthValidator静态方法进行校验
        testWithAuthValidator(pluginId, pluginSecret, userAccessToken, userKey);

        // 方式2：Token类型检测
        testTokenTypeDetection();

        // 方式3：简单的连接测试
        testSimpleConnection(pluginId, pluginSecret, userAccessToken);
    }

    /**
     * 简单的连接测试示例
     */
    private static void testSimpleConnection(String pluginId, String pluginSecret, String userAccessToken) {
        System.out.println("=== 简单的连接测试示例 ===");

        try {
            // 测试Plugin凭证
            boolean isPluginValid = testConnection(pluginId, pluginSecret);
            System.out.println("Plugin凭证是否有效: " + isPluginValid);

            // 测试用户Token
            if (userAccessToken != null && !userAccessToken.isEmpty()) {
                boolean isUserTokenValid = testUserToken(userAccessToken, pluginId, pluginSecret);
                System.out.println("用户Token是否有效: " + isUserTokenValid);
            }
        } catch (Exception e) {
            System.out.println("测试过程中发生异常: " + e.getMessage());
        }
    }

    /**
     * 使用AuthValidator静态方法进行校验
     */
    private static void testWithAuthValidator(String pluginId, String pluginSecret, String userAccessToken, String userKey) {
        System.out.println("=== 使用AuthValidator静态方法进行校验 ===");

        // 1. 仅校验Plugin凭证（不验证User Key）
        AuthValidationResult pluginResult = AuthValidator.validatePluginCredentials(pluginId, pluginSecret);
        System.out.println("Plugin凭证校验结果: " + pluginResult);

        // 2. 校验Plugin凭证 + User Key组合
        if (userKey != null && !userKey.isEmpty()) {
            AuthValidationResult pluginWithUserResult = AuthValidator.validatePluginCredentials(pluginId, pluginSecret, userKey, null);
            System.out.println("Plugin凭证+User Key校验结果: " + pluginWithUserResult);
        }

        // 3. 仅校验虚拟Plugin凭证（不验证User Key）
        AuthValidationResult virtualPluginResult = AuthValidator.validateVirtualPluginCredentials(pluginId, pluginSecret);
        System.out.println("虚拟Plugin凭证校验结果: " + virtualPluginResult);

        // 4. 校验虚拟Plugin凭证 + User Key组合
        if (userKey != null && !userKey.isEmpty()) {
            AuthValidationResult virtualWithUserResult = AuthValidator.validateVirtualPluginCredentials(pluginId, pluginSecret, userKey, null);
            System.out.println("虚拟Plugin凭证+User Key校验结果: " + virtualWithUserResult);
        }

        // 5. 校验用户访问凭证（独立使用）
        if (userAccessToken != null && !userAccessToken.isEmpty()) {
            AuthValidationResult userResult = AuthValidator.validateUserAccessToken(
                    userAccessToken, pluginId, pluginSecret, null);
            System.out.println("用户访问凭证校验结果: " + userResult);
        }
    }

    /**
     * Token类型检测示例
     */
    private static void testTokenTypeDetection() {
        System.out.println("=== Token类型检测示例 ===");

        String[] testTokens = {
                "p-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",  // plugin_access_token
                "v-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",  // virtual_plugin_token
                "u-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",  // user_access_token
                "invalid-token",                            // 无效token
                ""                                          // 空token
        };

        for (String token : testTokens) {
            TokenTypeDetector.TokenType type = TokenTypeDetector.detectTokenType(token);
            System.out.println("Token: " + token + " -> 类型: " + type);

            // 格式校验
            boolean isValid = AuthValidator.isValidTokenFormat(token);
            System.out.println("格式是否有效: " + isValid);
            System.out.println();
        }
    }

    /**
     * 简单的连接测试方法
     * 业务方可以直接使用这个方法进行快速测试
     */
    public static boolean testConnection(String pluginId, String pluginSecret) {
        try {
            AuthValidationResult result = AuthValidator.validatePluginCredentials(pluginId, pluginSecret);
            if (result.isSuccess()) {
                System.out.println("连接测试成功: " + result.getMessage());
                return true;
            } else {
                System.out.println("连接测试失败: " + result.getMessage());
                return false;
            }
        } catch (Exception e) {
            System.out.println("连接测试异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 测试用户token是否有效
     * 注意：pluginId和pluginSecret可以传空字符串，因为实际验证只使用userAccessToken
     */
    public static boolean testUserToken(String userAccessToken, String pluginId, String pluginSecret) {
        try {
            // 对于User Access Token验证，可以传入空字符串作为占位符
            String dummyPluginId = (pluginId != null && !pluginId.isEmpty()) ? pluginId : "";
            String dummyPluginSecret = (pluginSecret != null && !pluginSecret.isEmpty()) ? pluginSecret : "";

            AuthValidationResult result = AuthValidator.validateUserAccessToken(
                    userAccessToken, dummyPluginId, dummyPluginSecret, null);
            if (result.isSuccess()) {
                System.out.println("用户token测试成功: " + result.getMessage());
                return true;
            } else {
                System.out.println("用户token测试失败: " + result.getMessage());
                return false;
            }
        } catch (Exception e) {
            System.out.println("用户token测试异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 测试Plugin凭证+User Key组合是否有效
     */
    public static boolean testPluginWithUserKey(String pluginId, String pluginSecret, String userKey) {
        try {
            AuthValidationResult result = AuthValidator.validatePluginCredentials(pluginId, pluginSecret, userKey, null);
            if (result.isSuccess()) {
                System.out.println("Plugin凭证+User Key测试成功: " + result.getMessage());
                return true;
            } else {
                System.out.println("Plugin凭证+User Key测试失败: " + result.getMessage());
                return false;
            }
        } catch (Exception e) {
            System.out.println("Plugin凭证+User Key测试异常: " + e.getMessage());
            return false;
        }
    }

    /**
     * 测试虚拟Plugin凭证+User Key组合是否有效
     */
    public static boolean testVirtualPluginWithUserKey(String pluginId, String pluginSecret, String userKey) {
        try {
            AuthValidationResult result = AuthValidator.validateVirtualPluginCredentials(pluginId, pluginSecret, userKey, null);
            if (result.isSuccess()) {
                System.out.println("虚拟Plugin凭证+User Key测试成功: " + result.getMessage());
                return true;
            } else {
                System.out.println("虚拟Plugin凭证+User Key测试失败: " + result.getMessage());
                return false;
            }
        } catch (Exception e) {
            System.out.println("虚拟Plugin凭证+User Key测试异常: " + e.getMessage());
            return false;
        }
    }
}
